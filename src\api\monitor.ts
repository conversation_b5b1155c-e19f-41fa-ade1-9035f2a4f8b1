import { http } from "@/utils/http";

export type ServerInfo = {
  success: boolean;
  data: {
    /** CPU信息 */
    cpu: {
      name: string;
      cores: number;
      usage: number;
    };
    /** 内存信息 */
    memory: {
      total: number;
      used: number;
      free: number;
      usage: number;
    };
    /** 磁盘信息 */
    disk: {
      total: number;
      used: number;
      free: number;
      usage: number;
    };
    /** 网络信息 */
    network: {
      upload: number;
      download: number;
    };
    /** 系统信息 */
    system: {
      os: string;
      arch: string;
      hostname: string;
      uptime: number;
    };
  };
};

export type OnlineUserResult = {
  success: boolean;
  data: {
    list: Array<{
      /** 会话ID */
      sessionId: string;
      /** 用户ID */
      userId: number;
      /** 用户名 */
      username: string;
      /** 昵称 */
      nickname: string;
      /** 部门名称 */
      deptName: string;
      /** IP地址 */
      ipaddr: string;
      /** 登录地点 */
      loginLocation: string;
      /** 浏览器 */
      browser: string;
      /** 操作系统 */
      os: string;
      /** 登录时间 */
      loginTime: string;
    }>;
    total: number;
  };
};

export type OperLogResult = {
  success: boolean;
  data: {
    list: Array<{
      /** 日志ID */
      id: number;
      /** 操作模块 */
      title: string;
      /** 业务类型 */
      businessType: string;
      /** 请求方法 */
      method: string;
      /** 请求方式 */
      requestMethod: string;
      /** 操作人员 */
      operName: string;
      /** 部门名称 */
      deptName: string;
      /** 请求URL */
      operUrl: string;
      /** 操作地址 */
      operIp: string;
      /** 操作地点 */
      operLocation: string;
      /** 请求参数 */
      operParam: string;
      /** 返回参数 */
      jsonResult: string;
      /** 操作状态 */
      status: number;
      /** 错误消息 */
      errorMsg: string;
      /** 操作时间 */
      operTime: string;
    }>;
    total: number;
  };
};

export type LoginLogResult = {
  success: boolean;
  data: {
    list: Array<{
      /** 日志ID */
      id: number;
      /** 用户名 */
      username: string;
      /** 登录IP */
      ipaddr: string;
      /** 登录地点 */
      loginLocation: string;
      /** 浏览器类型 */
      browser: string;
      /** 操作系统 */
      os: string;
      /** 登录状态 */
      status: number;
      /** 提示消息 */
      msg: string;
      /** 登录时间 */
      loginTime: string;
    }>;
    total: number;
  };
};

/** 服务器监控 */
export const getServerInfo = () => {
  return http.request<ServerInfo>("get", "/monitor/server");
};

/** 在线用户 */
export const getOnlineUserList = (data?: object) => {
  return http.request<OnlineUserResult>("post", "/monitor/online/list", { data });
};

export const forceLogout = (data?: object) => {
  return http.request<any>("post", "/monitor/online/forceLogout", { data });
};

/** 操作日志 */
export const getOperLogList = (data?: object) => {
  return http.request<OperLogResult>("post", "/monitor/operlog/list", { data });
};

export const deleteOperLog = (data?: object) => {
  return http.request<any>("post", "/monitor/operlog/delete", { data });
};

export const clearOperLog = () => {
  return http.request<any>("post", "/monitor/operlog/clear");
};

export const exportOperLog = (data?: object) => {
  return http.request<any>("post", "/monitor/operlog/export", { data });
};

/** 登录日志 */
export const getLoginLogList = (data?: object) => {
  return http.request<LoginLogResult>("post", "/monitor/loginlog/list", { data });
};

export const deleteLoginLog = (data?: object) => {
  return http.request<any>("post", "/monitor/loginlog/delete", { data });
};

export const clearLoginLog = () => {
  return http.request<any>("post", "/monitor/loginlog/clear");
};

export const exportLoginLog = (data?: object) => {
  return http.request<any>("post", "/monitor/loginlog/export", { data });
};
