<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getMenuList, addMenu, updateMenu, deleteMenu } from "@/api/system";
import MenuForm from "./components/MenuForm.vue";

import AddFill from "~icons/ri/add-circle-line";
import EditPen from "~icons/ep/edit-pen";
import Delete from "~icons/ep/delete";
import Refresh from "~icons/ep/refresh";
import Search from "~icons/ri/search-line";
import FolderAdd from "~icons/ep/folder-add";

defineOptions({
  name: "SystemMenu"
});

const loading = ref(false);
const tableRef = ref();
const formRef = ref();

// 搜索表单
const searchForm = ref({
  title: "",
  status: ""
});

// 表格数据
const tableData = ref([]);

// 表格列配置
const columns = ref([
  {
    label: "菜单名称",
    prop: "title",
    minWidth: 200,
    align: "left"
  },
  {
    label: "图标",
    prop: "icon",
    width: 80,
    align: "center",
    cellRenderer: ({ row }) => (
      row.icon ? (
        <el-icon size={18}>
          <component is={row.icon} />
        </el-icon>
      ) : (
        <span>-</span>
      )
    )
  },
  {
    label: "排序",
    prop: "sort",
    width: 80,
    align: "center"
  },
  {
    label: "权限标识",
    prop: "permission",
    minWidth: 150
  },
  {
    label: "组件路径",
    prop: "component",
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    label: "状态",
    prop: "status",
    width: 80,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-tag type={row.status === 1 ? "success" : "danger"}>
        {row.status === 1 ? "启用" : "禁用"}
      </el-tag>
    )
  },
  {
    label: "创建时间",
    prop: "createTime",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
]);

// 获取菜单列表
const getTableData = async () => {
  loading.value = true;
  try {
    const { data } = await getMenuList(searchForm.value);
    tableData.value = buildMenuTree(data);
  } catch (error) {
    console.error("获取菜单列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 构建菜单树
const buildMenuTree = (menus: any[], parentId = 0) => {
  const result: any[] = [];
  menus.forEach(menu => {
    if (menu.parentId === parentId) {
      const children = buildMenuTree(menus, menu.id);
      if (children.length > 0) {
        menu.children = children;
      }
      result.push(menu);
    }
  });
  return result;
};

// 搜索
const handleSearch = () => {
  getTableData();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    title: "",
    status: ""
  };
  getTableData();
};

// 新增菜单
const handleAdd = (parentId?: number) => {
  formRef.value?.openDialog("add", null, parentId);
};

// 编辑菜单
const handleEdit = (row: any) => {
  formRef.value?.openDialog("edit", row);
};

// 删除菜单
const handleDelete = async (row: any) => {
  // 检查是否有子菜单
  if (row.children && row.children.length > 0) {
    ElMessage.warning("该菜单下存在子菜单，请先删除子菜单");
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确认删除菜单"${row.title}"吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    await deleteMenu({ id: row.id });
    ElMessage.success("删除成功");
    getTableData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

// 表单提交成功
const handleFormSuccess = () => {
  getTableData();
};

onMounted(() => {
  getTableData();
});
</script>

<template>
  <div>
    <!-- 搜索表单 -->
    <el-form
      :model="searchForm"
      :inline="true"
      class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="菜单名称：" prop="title">
        <el-input
          v-model="searchForm.title"
          placeholder="请输入菜单名称"
          clearable
          class="w-[180px]"
        />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="w-[180px]"
        >
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="handleReset">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="菜单管理" :columns="columns" @refresh="getTableData">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="handleAdd()"
        >
          新增菜单
        </el-button>
      </template>
      
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          row-key="id"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
        >
          <template #operation="{ row }">
            <el-button
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(FolderAdd)"
              @click="handleAdd(row.id)"
            >
              新增
            </el-button>
            <el-button
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(EditPen)"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon(Delete)"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>

  <!-- 菜单表单弹窗 -->
  <MenuForm ref="formRef" @success="handleFormSuccess" />
</template>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
