const Layout = () => import("@/layout/index.vue");

export default {
  path: "/monitor",
  name: "Monitor",
  component: Layout,
  redirect: "/monitor/server",
  meta: {
    icon: "ep/monitor",
    title: "系统监控",
    rank: 11
  },
  children: [
    {
      path: "/monitor/server",
      name: "MonitorServer",
      component: () => import("@/views/monitor/server/index.vue"),
      meta: {
        title: "服务器监控",
        auths: ["btn_view"]
      }
    },
    {
      path: "/monitor/online",
      name: "MonitorOnline",
      component: () => import("@/views/monitor/online/index.vue"),
      meta: {
        title: "在线用户",
        auths: ["btn_view", "btn_force_logout"]
      }
    },
    {
      path: "/monitor/operlog",
      name: "MonitorOperlog",
      component: () => import("@/views/monitor/operlog/index.vue"),
      meta: {
        title: "操作日志",
        auths: ["btn_view", "btn_export", "btn_delete"]
      }
    },
    {
      path: "/monitor/loginlog",
      name: "MonitorLoginlog",
      component: () => import("@/views/monitor/loginlog/index.vue"),
      meta: {
        title: "登录日志",
        auths: ["btn_view", "btn_export", "btn_delete"]
      }
    }
  ]
} satisfies RouteConfigsTable;
