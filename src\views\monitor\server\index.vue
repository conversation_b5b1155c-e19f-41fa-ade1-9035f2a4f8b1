<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { getServerInfo } from "@/api/monitor";

defineOptions({
  name: "MonitorServer"
});

const loading = ref(false);
const serverInfo = ref<any>({
  cpu: { name: "", cores: 0, usage: 0 },
  memory: { total: 0, used: 0, free: 0, usage: 0 },
  disk: { total: 0, used: 0, free: 0, usage: 0 },
  network: { upload: 0, download: 0 },
  system: { os: "", arch: "", hostname: "", uptime: 0 }
});

let timer: NodeJS.Timeout | null = null;

// 获取服务器信息
const getServerData = async () => {
  loading.value = true;
  try {
    const { data } = await getServerInfo();
    serverInfo.value = data;
  } catch (error) {
    console.error("获取服务器信息失败:", error);
  } finally {
    loading.value = false;
  }
};

// 格式化字节大小
const formatBytes = (bytes: number) => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 格式化运行时间
const formatUptime = (seconds: number) => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${days}天 ${hours}小时 ${minutes}分钟`;
};

// 获取使用率颜色
const getUsageColor = (usage: number) => {
  if (usage < 50) return "#67C23A";
  if (usage < 80) return "#E6A23C";
  return "#F56C6C";
};

onMounted(() => {
  getServerData();
  // 每5秒刷新一次数据
  timer = setInterval(() => {
    getServerData();
  }, 5000);
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<template>
  <div v-loading="loading" class="p-4">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <!-- CPU 使用率 -->
      <el-card class="h-32">
        <div class="flex items-center justify-between h-full">
          <div>
            <div class="text-gray-500 text-sm">CPU 使用率</div>
            <div class="text-2xl font-bold mt-2">{{ serverInfo.cpu.usage }}%</div>
            <div class="text-gray-400 text-xs">{{ serverInfo.cpu.cores }} 核心</div>
          </div>
          <el-progress
            type="circle"
            :percentage="serverInfo.cpu.usage"
            :width="60"
            :color="getUsageColor(serverInfo.cpu.usage)"
          />
        </div>
      </el-card>

      <!-- 内存使用率 -->
      <el-card class="h-32">
        <div class="flex items-center justify-between h-full">
          <div>
            <div class="text-gray-500 text-sm">内存使用率</div>
            <div class="text-2xl font-bold mt-2">{{ serverInfo.memory.usage }}%</div>
            <div class="text-gray-400 text-xs">
              {{ formatBytes(serverInfo.memory.used) }} / {{ formatBytes(serverInfo.memory.total) }}
            </div>
          </div>
          <el-progress
            type="circle"
            :percentage="serverInfo.memory.usage"
            :width="60"
            :color="getUsageColor(serverInfo.memory.usage)"
          />
        </div>
      </el-card>

      <!-- 磁盘使用率 -->
      <el-card class="h-32">
        <div class="flex items-center justify-between h-full">
          <div>
            <div class="text-gray-500 text-sm">磁盘使用率</div>
            <div class="text-2xl font-bold mt-2">{{ serverInfo.disk.usage }}%</div>
            <div class="text-gray-400 text-xs">
              {{ formatBytes(serverInfo.disk.used) }} / {{ formatBytes(serverInfo.disk.total) }}
            </div>
          </div>
          <el-progress
            type="circle"
            :percentage="serverInfo.disk.usage"
            :width="60"
            :color="getUsageColor(serverInfo.disk.usage)"
          />
        </div>
      </el-card>

      <!-- 网络流量 -->
      <el-card class="h-32">
        <div class="flex items-center justify-between h-full">
          <div>
            <div class="text-gray-500 text-sm">网络流量</div>
            <div class="text-sm font-bold mt-2">
              <div class="text-green-500">↑ {{ formatBytes(serverInfo.network.upload) }}/s</div>
              <div class="text-blue-500">↓ {{ formatBytes(serverInfo.network.download) }}/s</div>
            </div>
          </div>
          <div class="text-4xl text-gray-300">
            <el-icon><Connection /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 系统信息 -->
    <el-card>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="font-medium">系统信息</span>
          <el-button type="primary" size="small" @click="getServerData">
            刷新
          </el-button>
        </div>
      </template>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="border-r border-gray-200 pr-4">
          <div class="text-gray-500 text-sm mb-2">操作系统</div>
          <div class="font-medium">{{ serverInfo.system.os }}</div>
        </div>
        
        <div class="border-r border-gray-200 pr-4">
          <div class="text-gray-500 text-sm mb-2">系统架构</div>
          <div class="font-medium">{{ serverInfo.system.arch }}</div>
        </div>
        
        <div class="border-r border-gray-200 pr-4">
          <div class="text-gray-500 text-sm mb-2">主机名</div>
          <div class="font-medium">{{ serverInfo.system.hostname }}</div>
        </div>
        
        <div>
          <div class="text-gray-500 text-sm mb-2">运行时间</div>
          <div class="font-medium">{{ formatUptime(serverInfo.system.uptime) }}</div>
        </div>
      </div>
    </el-card>

    <!-- CPU 详细信息 -->
    <el-card class="mt-4">
      <template #header>
        <span class="font-medium">CPU 详细信息</span>
      </template>
      
      <div class="space-y-4">
        <div>
          <div class="text-gray-500 text-sm mb-2">处理器型号</div>
          <div class="font-medium">{{ serverInfo.cpu.name }}</div>
        </div>
        
        <div>
          <div class="text-gray-500 text-sm mb-2">核心数量</div>
          <div class="font-medium">{{ serverInfo.cpu.cores }} 核心</div>
        </div>
        
        <div>
          <div class="text-gray-500 text-sm mb-2">使用率</div>
          <el-progress
            :percentage="serverInfo.cpu.usage"
            :color="getUsageColor(serverInfo.cpu.usage)"
            :show-text="true"
            :format="(percentage) => `${percentage}%`"
          />
        </div>
      </div>
    </el-card>

    <!-- 内存详细信息 -->
    <el-card class="mt-4">
      <template #header>
        <span class="font-medium">内存详细信息</span>
      </template>
      
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div class="text-gray-500 text-sm mb-2">总内存</div>
            <div class="font-medium">{{ formatBytes(serverInfo.memory.total) }}</div>
          </div>
          
          <div>
            <div class="text-gray-500 text-sm mb-2">已使用</div>
            <div class="font-medium text-red-500">{{ formatBytes(serverInfo.memory.used) }}</div>
          </div>
          
          <div>
            <div class="text-gray-500 text-sm mb-2">可用内存</div>
            <div class="font-medium text-green-500">{{ formatBytes(serverInfo.memory.free) }}</div>
          </div>
        </div>
        
        <div>
          <div class="text-gray-500 text-sm mb-2">使用率</div>
          <el-progress
            :percentage="serverInfo.memory.usage"
            :color="getUsageColor(serverInfo.memory.usage)"
            :show-text="true"
            :format="(percentage) => `${percentage}%`"
          />
        </div>
      </div>
    </el-card>

    <!-- 磁盘详细信息 -->
    <el-card class="mt-4">
      <template #header>
        <span class="font-medium">磁盘详细信息</span>
      </template>
      
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div class="text-gray-500 text-sm mb-2">总容量</div>
            <div class="font-medium">{{ formatBytes(serverInfo.disk.total) }}</div>
          </div>
          
          <div>
            <div class="text-gray-500 text-sm mb-2">已使用</div>
            <div class="font-medium text-red-500">{{ formatBytes(serverInfo.disk.used) }}</div>
          </div>
          
          <div>
            <div class="text-gray-500 text-sm mb-2">可用空间</div>
            <div class="font-medium text-green-500">{{ formatBytes(serverInfo.disk.free) }}</div>
          </div>
        </div>
        
        <div>
          <div class="text-gray-500 text-sm mb-2">使用率</div>
          <el-progress
            :percentage="serverInfo.disk.usage"
            :color="getUsageColor(serverInfo.disk.usage)"
            :show-text="true"
            :format="(percentage) => `${percentage}%`"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.gap-4 {
  gap: 1rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
