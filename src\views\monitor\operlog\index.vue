<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getOperLogList, deleteOperLog, clearOperLog, exportOperLog } from "@/api/monitor";

import Refresh from "~icons/ep/refresh";
import Search from "~icons/ri/search-line";
import Delete from "~icons/ep/delete";
import Download from "~icons/ep/download";
import View from "~icons/ep/view";

defineOptions({
  name: "MonitorOperlog"
});

const loading = ref(false);
const tableRef = ref();

// 搜索表单
const searchForm = ref({
  title: "",
  operName: "",
  status: "",
  startTime: "",
  endTime: ""
});

// 表格数据
const tableData = ref([]);
const total = ref(0);
const pagination = ref({
  page: 1,
  size: 10
});

// 详情弹窗
const detailVisible = ref(false);
const detailData = ref<any>({});

// 表格列配置
const columns = ref([
  {
    label: "操作模块",
    prop: "title",
    minWidth: 120
  },
  {
    label: "业务类型",
    prop: "businessType",
    minWidth: 100
  },
  {
    label: "请求方式",
    prop: "requestMethod",
    width: 80,
    align: "center"
  },
  {
    label: "操作人员",
    prop: "operName",
    minWidth: 100
  },
  {
    label: "操作地址",
    prop: "operIp",
    minWidth: 130
  },
  {
    label: "操作地点",
    prop: "operLocation",
    minWidth: 120
  },
  {
    label: "操作状态",
    prop: "status",
    width: 80,
    align: "center",
    cellRenderer: ({ row }) => (
      <el-tag type={row.status === 1 ? "success" : "danger"}>
        {row.status === 1 ? "成功" : "失败"}
      </el-tag>
    )
  },
  {
    label: "操作时间",
    prop: "operTime",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 150,
    slot: "operation"
  }
]);

// 获取操作日志列表
const getTableData = async () => {
  loading.value = true;
  try {
    const { data } = await getOperLogList({
      ...searchForm.value,
      page: pagination.value.page,
      size: pagination.value.size
    });
    tableData.value = data.list;
    total.value = data.total;
  } catch (error) {
    console.error("获取操作日志列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.value.page = 1;
  getTableData();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    title: "",
    operName: "",
    status: "",
    startTime: "",
    endTime: ""
  };
  pagination.value.page = 1;
  getTableData();
};

// 查看详情
const handleView = (row: any) => {
  detailData.value = row;
  detailVisible.value = true;
};

// 删除日志
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确认删除该操作日志吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    await deleteOperLog({ id: row.id });
    ElMessage.success("删除成功");
    getTableData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

// 清空日志
const handleClear = async () => {
  try {
    await ElMessageBox.confirm(
      "确认清空所有操作日志吗？此操作不可恢复！",
      "警告",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    await clearOperLog();
    ElMessage.success("清空成功");
    getTableData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("清空失败");
    }
  }
};

// 导出日志
const handleExport = async () => {
  try {
    await exportOperLog(searchForm.value);
    ElMessage.success("导出成功");
  } catch (error) {
    ElMessage.error("导出失败");
  }
};

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.value.size = size;
  getTableData();
};

const handleCurrentChange = (page: number) => {
  pagination.value.page = page;
  getTableData();
};

onMounted(() => {
  getTableData();
});
</script>

<template>
  <div>
    <!-- 搜索表单 -->
    <el-form
      :model="searchForm"
      :inline="true"
      class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="操作模块：" prop="title">
        <el-input
          v-model="searchForm.title"
          placeholder="请输入操作模块"
          clearable
          class="w-[180px]"
        />
      </el-form-item>
      <el-form-item label="操作人员：" prop="operName">
        <el-input
          v-model="searchForm.operName"
          placeholder="请输入操作人员"
          clearable
          class="w-[180px]"
        />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="w-[180px]"
        >
          <el-option label="成功" value="1" />
          <el-option label="失败" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间：">
        <el-date-picker
          v-model="searchForm.startTime"
          type="datetime"
          placeholder="开始时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-[180px]"
        />
        <span class="mx-2">至</span>
        <el-date-picker
          v-model="searchForm.endTime"
          type="datetime"
          placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-[180px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="handleReset">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="操作日志" :columns="columns" @refresh="getTableData">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon(Download)"
          @click="handleExport"
        >
          导出
        </el-button>
        <el-button
          type="danger"
          :icon="useRenderIcon(Delete)"
          @click="handleClear"
        >
          清空
        </el-button>
      </template>
      
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(View)"
              @click="handleView(row)"
            >
              详情
            </el-button>
            <el-button
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon(Delete)"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="操作日志详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="操作模块">
          {{ detailData.title }}
        </el-descriptions-item>
        <el-descriptions-item label="业务类型">
          {{ detailData.businessType }}
        </el-descriptions-item>
        <el-descriptions-item label="请求方法">
          {{ detailData.method }}
        </el-descriptions-item>
        <el-descriptions-item label="请求方式">
          <el-tag>{{ detailData.requestMethod }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作人员">
          {{ detailData.operName }}
        </el-descriptions-item>
        <el-descriptions-item label="部门名称">
          {{ detailData.deptName }}
        </el-descriptions-item>
        <el-descriptions-item label="请求URL" :span="2">
          {{ detailData.operUrl }}
        </el-descriptions-item>
        <el-descriptions-item label="操作地址">
          {{ detailData.operIp }}
        </el-descriptions-item>
        <el-descriptions-item label="操作地点">
          {{ detailData.operLocation }}
        </el-descriptions-item>
        <el-descriptions-item label="操作状态">
          <el-tag :type="detailData.status === 1 ? 'success' : 'danger'">
            {{ detailData.status === 1 ? "成功" : "失败" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作时间">
          {{ detailData.operTime }}
        </el-descriptions-item>
        <el-descriptions-item label="请求参数" :span="2">
          <el-input
            v-model="detailData.operParam"
            type="textarea"
            :rows="3"
            readonly
          />
        </el-descriptions-item>
        <el-descriptions-item label="返回参数" :span="2">
          <el-input
            v-model="detailData.jsonResult"
            type="textarea"
            :rows="3"
            readonly
          />
        </el-descriptions-item>
        <el-descriptions-item v-if="detailData.errorMsg" label="错误消息" :span="2">
          <el-input
            v-model="detailData.errorMsg"
            type="textarea"
            :rows="2"
            readonly
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
