<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { addUser, updateUser, getDeptList, getRoleList } from "@/api/system";

const emit = defineEmits<{
  success: [];
}>();

const dialogVisible = ref(false);
const dialogTitle = ref("新增用户");
const formRef = ref<FormInstance>();
const loading = ref(false);
const isEdit = ref(false);

// 表单数据
const formData = reactive({
  id: "",
  username: "",
  nickname: "",
  password: "",
  phone: "",
  email: "",
  deptId: "",
  roleIds: [],
  status: 1,
  remark: ""
});

// 部门选项
const deptOptions = ref([]);
// 角色选项
const roleOptions = ref([]);

// 表单验证规则
const formRules = reactive<FormRules>({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 2, max: 20, message: "用户名长度在 2 到 20 个字符", trigger: "blur" }
  ],
  nickname: [
    { required: true, message: "请输入昵称", trigger: "blur" },
    { min: 2, max: 20, message: "昵称长度在 2 到 20 个字符", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" }
  ],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  email: [
    { required: true, message: "请输入邮箱", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" }
  ],
  deptId: [
    { required: true, message: "请选择部门", trigger: "change" }
  ]
});

// 获取部门列表
const getDeptOptions = async () => {
  try {
    const { data } = await getDeptList();
    deptOptions.value = data;
  } catch (error) {
    console.error("获取部门列表失败:", error);
  }
};

// 获取角色列表
const getRoleOptions = async () => {
  try {
    const { data } = await getRoleList();
    roleOptions.value = data.list;
  } catch (error) {
    console.error("获取角色列表失败:", error);
  }
};

// 打开弹窗
const openDialog = (type = "add", row?: any) => {
  dialogVisible.value = true;
  isEdit.value = type === "edit";
  dialogTitle.value = type === "edit" ? "编辑用户" : "新增用户";
  
  if (type === "edit" && row) {
    Object.assign(formData, {
      id: row.id,
      username: row.username,
      nickname: row.nickname,
      password: "",
      phone: row.phone,
      email: row.email,
      deptId: row.deptId,
      roleIds: row.roles?.map((role: any) => role.id) || [],
      status: row.status,
      remark: row.remark || ""
    });
    
    // 编辑时密码不是必填项
    formRules.password = [
      { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" }
    ];
  } else {
    resetForm();
    // 新增时密码是必填项
    formRules.password = [
      { required: true, message: "请输入密码", trigger: "blur" },
      { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" }
    ];
  }
  
  getDeptOptions();
  getRoleOptions();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: "",
    username: "",
    nickname: "",
    password: "",
    phone: "",
    email: "",
    deptId: "",
    roleIds: [],
    status: 1,
    remark: ""
  });
  formRef.value?.clearValidate();
};

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  
  loading.value = true;
  try {
    const submitData = { ...formData };
    
    if (isEdit.value) {
      // 编辑时如果密码为空则不传递密码字段
      if (!submitData.password) {
        delete submitData.password;
      }
      await updateUser(submitData);
      ElMessage.success("编辑成功");
    } else {
      await addUser(submitData);
      ElMessage.success("新增成功");
    }
    
    closeDialog();
    emit("success");
  } catch (error) {
    ElMessage.error(isEdit.value ? "编辑失败" : "新增失败");
  } finally {
    loading.value = false;
  }
};

// 递归构建部门树选项
const buildDeptTree = (depts: any[], parentId = 0) => {
  const result: any[] = [];
  depts.forEach(dept => {
    if (dept.parentId === parentId) {
      const children = buildDeptTree(depts, dept.id);
      if (children.length > 0) {
        dept.children = children;
      }
      result.push(dept);
    }
  });
  return result;
};

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formData.username"
              placeholder="请输入用户名"
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="昵称" prop="nickname">
            <el-input
              v-model="formData.nickname"
              placeholder="请输入昵称"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="formData.password"
              type="password"
              :placeholder="isEdit ? '留空则不修改密码' : '请输入密码'"
              show-password
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model="formData.phone"
              placeholder="请输入手机号"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="formData.email"
              placeholder="请输入邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门" prop="deptId">
            <el-tree-select
              v-model="formData.deptId"
              :data="buildDeptTree(deptOptions)"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children'
              }"
              placeholder="请选择部门"
              check-strictly
              :render-after-expand="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色" prop="roleIds">
            <el-select
              v-model="formData.roleIds"
              multiple
              placeholder="请选择角色"
              style="width: 100%"
            >
              <el-option
                v-for="role in roleOptions"
                :key="role.id"
                :label="role.name"
                :value="role.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
