import { http } from "@/utils/http";

export type UserResult = {
  success: boolean;
  data: {
    /** 用户列表 */
    list: Array<{
      /** 用户ID */
      id: number;
      /** 用户名 */
      username: string;
      /** 昵称 */
      nickname: string;
      /** 手机号 */
      phone: string;
      /** 邮箱 */
      email: string;
      /** 头像 */
      avatar: string;
      /** 状态 */
      status: number;
      /** 部门ID */
      deptId: number;
      /** 部门名称 */
      deptName: string;
      /** 角色列表 */
      roles: Array<{
        id: number;
        name: string;
      }>;
      /** 创建时间 */
      createTime: string;
    }>;
    /** 总条数 */
    total: number;
  };
};

export type RoleResult = {
  success: boolean;
  data: {
    /** 角色列表 */
    list: Array<{
      /** 角色ID */
      id: number;
      /** 角色名称 */
      name: string;
      /** 角色编码 */
      code: string;
      /** 角色描述 */
      description: string;
      /** 状态 */
      status: number;
      /** 排序 */
      sort: number;
      /** 创建时间 */
      createTime: string;
    }>;
    /** 总条数 */
    total: number;
  };
};

export type MenuResult = {
  success: boolean;
  data: Array<{
    /** 菜单ID */
    id: number;
    /** 父级ID */
    parentId: number;
    /** 菜单名称 */
    name: string;
    /** 菜单标题 */
    title: string;
    /** 菜单路径 */
    path: string;
    /** 组件路径 */
    component: string;
    /** 菜单图标 */
    icon: string;
    /** 菜单类型 */
    type: number;
    /** 排序 */
    sort: number;
    /** 状态 */
    status: number;
    /** 权限标识 */
    permission: string;
    /** 创建时间 */
    createTime: string;
    /** 子菜单 */
    children?: Array<any>;
  }>;
};

export type DeptResult = {
  success: boolean;
  data: Array<{
    /** 部门ID */
    id: number;
    /** 父级ID */
    parentId: number;
    /** 部门名称 */
    name: string;
    /** 部门编码 */
    code: string;
    /** 负责人 */
    leader: string;
    /** 联系电话 */
    phone: string;
    /** 邮箱 */
    email: string;
    /** 排序 */
    sort: number;
    /** 状态 */
    status: number;
    /** 创建时间 */
    createTime: string;
    /** 子部门 */
    children?: Array<any>;
  }>;
};

/** 用户管理 */
export const getUserList = (data?: object) => {
  return http.request<UserResult>("post", "/user/list", { data });
};

export const addUser = (data?: object) => {
  return http.request<any>("post", "/user/add", { data });
};

export const updateUser = (data?: object) => {
  return http.request<any>("post", "/user/update", { data });
};

export const deleteUser = (data?: object) => {
  return http.request<any>("post", "/user/delete", { data });
};

export const resetUserPassword = (data?: object) => {
  return http.request<any>("post", "/user/resetPassword", { data });
};

export const uploadUserAvatar = (data?: object) => {
  return http.request<any>("post", "/user/uploadAvatar", { data });
};

/** 角色管理 */
export const getRoleList = (data?: object) => {
  return http.request<RoleResult>("post", "/role/list", { data });
};

export const addRole = (data?: object) => {
  return http.request<any>("post", "/role/add", { data });
};

export const updateRole = (data?: object) => {
  return http.request<any>("post", "/role/update", { data });
};

export const deleteRole = (data?: object) => {
  return http.request<any>("post", "/role/delete", { data });
};

export const getRoleMenus = (data?: object) => {
  return http.request<any>("post", "/role/menus", { data });
};

export const setRoleMenus = (data?: object) => {
  return http.request<any>("post", "/role/setMenus", { data });
};

/** 菜单管理 */
export const getMenuList = (data?: object) => {
  return http.request<MenuResult>("post", "/menu/list", { data });
};

export const addMenu = (data?: object) => {
  return http.request<any>("post", "/menu/add", { data });
};

export const updateMenu = (data?: object) => {
  return http.request<any>("post", "/menu/update", { data });
};

export const deleteMenu = (data?: object) => {
  return http.request<any>("post", "/menu/delete", { data });
};

/** 部门管理 */
export const getDeptList = (data?: object) => {
  return http.request<DeptResult>("post", "/dept/list", { data });
};

export const addDept = (data?: object) => {
  return http.request<any>("post", "/dept/add", { data });
};

export const updateDept = (data?: object) => {
  return http.request<any>("post", "/dept/update", { data });
};

export const deleteDept = (data?: object) => {
  return http.request<any>("post", "/dept/delete", { data });
};
