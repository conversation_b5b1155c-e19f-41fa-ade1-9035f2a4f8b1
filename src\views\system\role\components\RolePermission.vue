<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { getMenuList, getRoleMenus, setRoleMenus } from "@/api/system";

const dialogVisible = ref(false);
const loading = ref(false);
const treeRef = ref();
const roleInfo = ref<any>({});

// 菜单树数据
const menuTree = ref([]);
const checkedKeys = ref([]);

const treeProps = {
  children: "children",
  label: "title"
};

// 获取菜单树
const getMenuTree = async () => {
  try {
    const { data } = await getMenuList();
    menuTree.value = buildMenuTree(data);
  } catch (error) {
    console.error("获取菜单树失败:", error);
  }
};

// 构建菜单树
const buildMenuTree = (menus: any[], parentId = 0) => {
  const result: any[] = [];
  menus.forEach(menu => {
    if (menu.parentId === parentId) {
      const children = buildMenuTree(menus, menu.id);
      if (children.length > 0) {
        menu.children = children;
      }
      result.push(menu);
    }
  });
  return result;
};

// 获取角色权限
const getRolePermissions = async (roleId: number) => {
  try {
    const { data } = await getRoleMenus({ roleId });
    checkedKeys.value = data.menuIds || [];
    // 设置选中的节点
    setTimeout(() => {
      treeRef.value?.setCheckedKeys(checkedKeys.value);
    }, 100);
  } catch (error) {
    console.error("获取角色权限失败:", error);
  }
};

// 打开弹窗
const openDialog = (role: any) => {
  dialogVisible.value = true;
  roleInfo.value = role;
  getMenuTree();
  getRolePermissions(role.id);
};

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false;
  roleInfo.value = {};
  checkedKeys.value = [];
};

// 保存权限配置
const handleSubmit = async () => {
  loading.value = true;
  try {
    const checkedNodes = treeRef.value?.getCheckedNodes() || [];
    const halfCheckedNodes = treeRef.value?.getHalfCheckedNodes() || [];
    
    // 获取所有选中的节点ID（包括半选中的父节点）
    const menuIds = [
      ...checkedNodes.map((node: any) => node.id),
      ...halfCheckedNodes.map((node: any) => node.id)
    ];
    
    await setRoleMenus({
      roleId: roleInfo.value.id,
      menuIds
    });
    
    ElMessage.success("权限配置成功");
    closeDialog();
  } catch (error) {
    ElMessage.error("权限配置失败");
  } finally {
    loading.value = false;
  }
};

// 全选/取消全选
const handleCheckAll = (checked: boolean) => {
  if (checked) {
    const allKeys = getAllNodeKeys(menuTree.value);
    treeRef.value?.setCheckedKeys(allKeys);
  } else {
    treeRef.value?.setCheckedKeys([]);
  }
};

// 获取所有节点的key
const getAllNodeKeys = (nodes: any[]): number[] => {
  const keys: number[] = [];
  nodes.forEach(node => {
    keys.push(node.id);
    if (node.children && node.children.length > 0) {
      keys.push(...getAllNodeKeys(node.children));
    }
  });
  return keys;
};

// 展开/折叠所有节点
const handleExpandAll = (expand: boolean) => {
  const allKeys = getAllNodeKeys(menuTree.value);
  allKeys.forEach(key => {
    const node = treeRef.value?.getNode(key);
    if (node) {
      node.expanded = expand;
    }
  });
};

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`权限配置 - ${roleInfo.name}`"
    width="600px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div class="mb-4">
      <el-button @click="handleCheckAll(true)">全选</el-button>
      <el-button @click="handleCheckAll(false)">取消全选</el-button>
      <el-button @click="handleExpandAll(true)">展开所有</el-button>
      <el-button @click="handleExpandAll(false)">折叠所有</el-button>
    </div>
    
    <div class="border border-gray-200 rounded p-4 max-h-96 overflow-auto">
      <el-tree
        ref="treeRef"
        :data="menuTree"
        :props="treeProps"
        show-checkbox
        node-key="id"
        :default-expand-all="true"
        :check-strictly="false"
      >
        <template #default="{ node, data }">
          <div class="flex items-center">
            <el-icon class="mr-1">
              <component :is="data.type === 1 ? 'Menu' : data.type === 2 ? 'Document' : 'Operation'" />
            </el-icon>
            <span>{{ node.label }}</span>
            <el-tag
              v-if="data.type"
              :type="data.type === 1 ? 'primary' : data.type === 2 ? 'success' : 'warning'"
              size="small"
              class="ml-2"
            >
              {{ data.type === 1 ? '目录' : data.type === 2 ? '菜单' : '按钮' }}
            </el-tag>
          </div>
        </template>
      </el-tree>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-tree-node__content) {
  height: 32px;
}
</style>
