<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { addRole, updateRole } from "@/api/system";

const emit = defineEmits<{
  success: [];
}>();

const dialogVisible = ref(false);
const dialogTitle = ref("新增角色");
const formRef = ref<FormInstance>();
const loading = ref(false);
const isEdit = ref(false);

// 表单数据
const formData = reactive({
  id: "",
  name: "",
  code: "",
  description: "",
  sort: 0,
  status: 1,
  remark: ""
});

// 表单验证规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入角色名称", trigger: "blur" },
    { min: 2, max: 20, message: "角色名称长度在 2 到 20 个字符", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入角色编码", trigger: "blur" },
    { min: 2, max: 20, message: "角色编码长度在 2 到 20 个字符", trigger: "blur" },
    { pattern: /^[A-Z_]+$/, message: "角色编码只能包含大写字母和下划线", trigger: "blur" }
  ],
  description: [
    { max: 200, message: "角色描述不能超过 200 个字符", trigger: "blur" }
  ],
  sort: [
    { required: true, message: "请输入排序", trigger: "blur" },
    { type: "number", min: 0, max: 9999, message: "排序必须是 0-9999 的数字", trigger: "blur" }
  ]
});

// 打开弹窗
const openDialog = (type = "add", row?: any) => {
  dialogVisible.value = true;
  isEdit.value = type === "edit";
  dialogTitle.value = type === "edit" ? "编辑角色" : "新增角色";
  
  if (type === "edit" && row) {
    Object.assign(formData, {
      id: row.id,
      name: row.name,
      code: row.code,
      description: row.description || "",
      sort: row.sort || 0,
      status: row.status,
      remark: row.remark || ""
    });
  } else {
    resetForm();
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: "",
    name: "",
    code: "",
    description: "",
    sort: 0,
    status: 1,
    remark: ""
  });
  formRef.value?.clearValidate();
};

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  
  loading.value = true;
  try {
    if (isEdit.value) {
      await updateRole(formData);
      ElMessage.success("编辑成功");
    } else {
      await addRole(formData);
      ElMessage.success("新增成功");
    }
    
    closeDialog();
    emit("success");
  } catch (error) {
    ElMessage.error(isEdit.value ? "编辑失败" : "新增失败");
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="角色名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入角色名称"
        />
      </el-form-item>
      
      <el-form-item label="角色编码" prop="code">
        <el-input
          v-model="formData.code"
          placeholder="请输入角色编码，如：ADMIN"
          :disabled="isEdit"
        />
      </el-form-item>
      
      <el-form-item label="角色描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入角色描述"
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="formData.sort"
              :min="0"
              :max="9999"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :value="1">启用</el-radio>
              <el-radio :value="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
