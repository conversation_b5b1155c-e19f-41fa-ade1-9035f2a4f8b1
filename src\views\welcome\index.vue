<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useUserStoreHook } from "@/store/modules/user";

defineOptions({
  name: "Welcome"
});

const userStore = useUserStoreHook();

// 统计数据
const statistics = ref({
  totalUsers: 1234,
  totalOrders: 5678,
  totalRevenue: 123456.78,
  todayVisits: 890
});

// 快捷操作
const quickActions = ref([
  {
    title: "用户管理",
    icon: "ep/user",
    path: "/system/user",
    color: "#409EFF"
  },
  {
    title: "角色管理",
    icon: "ep/avatar",
    path: "/system/role",
    color: "#67C23A"
  },
  {
    title: "菜单管理",
    icon: "ep/menu",
    path: "/system/menu",
    color: "#E6A23C"
  },
  {
    title: "部门管理",
    icon: "ep/office-building",
    path: "/system/dept",
    color: "#F56C6C"
  },
  {
    title: "服务器监控",
    icon: "ep/monitor",
    path: "/monitor/server",
    color: "#909399"
  },
  {
    title: "操作日志",
    icon: "ep/document",
    path: "/monitor/operlog",
    color: "#606266"
  }
]);

// 最近活动
const recentActivities = ref([
  {
    time: "2024-01-15 10:30",
    user: "管理员",
    action: "登录系统",
    type: "info"
  },
  {
    time: "2024-01-15 10:25",
    user: "张三",
    action: "新增用户",
    type: "success"
  },
  {
    time: "2024-01-15 10:20",
    user: "李四",
    action: "修改角色权限",
    type: "warning"
  },
  {
    time: "2024-01-15 10:15",
    user: "王五",
    action: "删除菜单",
    type: "danger"
  },
  { time: "2024-01-15 10:10", user: "赵六", action: "导出数据", type: "info" }
]);

// 系统信息
const systemInfo = ref({
  version: "1.0.0",
  buildTime: "2024-01-15",
  author: "Travel Admin",
  description: "基于 Vue3 + TypeScript + Element Plus 的后台管理系统"
});

// 获取当前时间问候语
const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 6) return "凌晨好";
  if (hour < 9) return "早上好";
  if (hour < 12) return "上午好";
  if (hour < 14) return "中午好";
  if (hour < 17) return "下午好";
  if (hour < 19) return "傍晚好";
  if (hour < 22) return "晚上好";
  return "夜深了";
};

const greeting = ref(getGreeting());

onMounted(() => {
  // 这里可以调用API获取真实数据
  console.log("Dashboard mounted");
});
</script>

<template>
  <div class="dashboard-container p-6">
    <!-- 欢迎区域 -->
    <div class="welcome-section mb-6">
      <el-card class="welcome-card">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">
              {{ greeting }}，{{ userStore.username || "用户" }}！
            </h2>
            <p class="text-gray-600">
              欢迎使用旅游管理系统，今天是个美好的一天，开始您的工作吧！
            </p>
          </div>
          <div class="text-6xl text-blue-500">
            <el-icon><Sunny /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-section mb-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <el-card class="stat-card">
          <div class="flex items-center">
            <div class="stat-icon bg-blue-100 text-blue-600">
              <el-icon size="24"><User /></el-icon>
            </div>
            <div class="ml-4">
              <div class="text-2xl font-bold">
                {{ statistics.totalUsers.toLocaleString() }}
              </div>
              <div class="text-gray-500 text-sm">总用户数</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="flex items-center">
            <div class="stat-icon bg-green-100 text-green-600">
              <el-icon size="24"><ShoppingCart /></el-icon>
            </div>
            <div class="ml-4">
              <div class="text-2xl font-bold">
                {{ statistics.totalOrders.toLocaleString() }}
              </div>
              <div class="text-gray-500 text-sm">总订单数</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="flex items-center">
            <div class="stat-icon bg-yellow-100 text-yellow-600">
              <el-icon size="24"><Money /></el-icon>
            </div>
            <div class="ml-4">
              <div class="text-2xl font-bold">
                ¥{{ statistics.totalRevenue.toLocaleString() }}
              </div>
              <div class="text-gray-500 text-sm">总收入</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="flex items-center">
            <div class="stat-icon bg-purple-100 text-purple-600">
              <el-icon size="24"><View /></el-icon>
            </div>
            <div class="ml-4">
              <div class="text-2xl font-bold">
                {{ statistics.todayVisits.toLocaleString() }}
              </div>
              <div class="text-gray-500 text-sm">今日访问</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 快捷操作 -->
        <div class="lg:col-span-2">
          <el-card>
            <template #header>
              <div class="flex items-center">
                <el-icon class="mr-2"><Operation /></el-icon>
                <span class="font-medium">快捷操作</span>
              </div>
            </template>

            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div
                v-for="action in quickActions"
                :key="action.title"
                class="quick-action-item"
                @click="$router.push(action.path)"
              >
                <div
                  class="quick-action-icon"
                  :style="{
                    backgroundColor: action.color + '20',
                    color: action.color
                  }"
                >
                  <el-icon size="24"><component :is="action.icon" /></el-icon>
                </div>
                <div class="text-sm font-medium mt-2">{{ action.title }}</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 最近活动 -->
        <div>
          <el-card>
            <template #header>
              <div class="flex items-center">
                <el-icon class="mr-2"><Clock /></el-icon>
                <span class="font-medium">最近活动</span>
              </div>
            </template>

            <div class="space-y-3">
              <div
                v-for="activity in recentActivities"
                :key="activity.time"
                class="flex items-start space-x-3"
              >
                <el-tag :type="activity.type" size="small" class="mt-1">
                  {{ activity.user }}
                </el-tag>
                <div class="flex-1">
                  <div class="text-sm">{{ activity.action }}</div>
                  <div class="text-xs text-gray-500">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="system-info mt-6">
      <el-card>
        <template #header>
          <div class="flex items-center">
            <el-icon class="mr-2"><InfoFilled /></el-icon>
            <span class="font-medium">系统信息</span>
          </div>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <div class="text-gray-500 text-sm mb-1">系统版本</div>
            <div class="font-medium">{{ systemInfo.version }}</div>
          </div>
          <div>
            <div class="text-gray-500 text-sm mb-1">构建时间</div>
            <div class="font-medium">{{ systemInfo.buildTime }}</div>
          </div>
          <div>
            <div class="text-gray-500 text-sm mb-1">开发者</div>
            <div class="font-medium">{{ systemInfo.author }}</div>
          </div>
          <div>
            <div class="text-gray-500 text-sm mb-1">技术栈</div>
            <div class="font-medium">Vue3 + TS + Element Plus</div>
          </div>
        </div>

        <div class="mt-4 pt-4 border-t border-gray-200">
          <div class="text-gray-500 text-sm mb-1">系统描述</div>
          <div class="text-sm">{{ systemInfo.description }}</div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dashboard-container {
  min-height: calc(100vh - 200px);
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  :deep(.el-card__body) {
    padding: 2rem;
  }
}

.stat-card {
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
  }
}

.quick-action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}
</style>
