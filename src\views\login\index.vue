<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import { ref, reactive, toRaw, onMounted } from "vue";
import { debounce } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import { useEventListener } from "@vueuse/core";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { initRouter, getTopMenu } from "@/router/utils";
import { bg, avatar, illustration } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { getCaptcha, forgotPassword } from "@/api/user";
import { ElMessage, ElMessageBox } from "element-plus";

import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import Lock from "~icons/ri/lock-fill";
import User from "~icons/ri/user-3-fill";

defineOptions({
  name: "Login"
});

const router = useRouter();
const loading = ref(false);
const disabled = ref(false);
const ruleFormRef = ref<FormInstance>();

const { initStorage } = useLayout();
initStorage();

const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title } = useNav();

const ruleForm = reactive({
  username: "admin",
  password: "admin123",
  captcha: "",
  captchaKey: "",
  rememberMe: false
});

// 验证码相关
const captchaImg = ref("");
const captchaLoading = ref(false);

// 忘记密码弹窗
const forgotPasswordVisible = ref(false);
const forgotPasswordForm = reactive({
  email: ""
});
const forgotPasswordLoading = ref(false);

// 获取验证码
const getCaptchaImage = async () => {
  captchaLoading.value = true;
  try {
    const { data } = await getCaptcha();
    captchaImg.value = data.img;
    ruleForm.captchaKey = data.key;
    ruleForm.captcha = "";
  } catch (error) {
    console.error("获取验证码失败:", error);
  } finally {
    captchaLoading.value = false;
  }
};

// 忘记密码
const handleForgotPassword = () => {
  forgotPasswordVisible.value = true;
};

// 提交忘记密码
const submitForgotPassword = async () => {
  if (!forgotPasswordForm.email) {
    ElMessage.warning("请输入邮箱地址");
    return;
  }

  forgotPasswordLoading.value = true;
  try {
    await forgotPassword({ email: forgotPasswordForm.email });
    ElMessage.success("重置密码邮件已发送，请查收邮箱");
    forgotPasswordVisible.value = false;
    forgotPasswordForm.email = "";
  } catch (error) {
    ElMessage.error("发送失败，请稍后重试");
  } finally {
    forgotPasswordLoading.value = false;
  }
};

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;

      // 保存记住密码状态
      if (ruleForm.rememberMe) {
        localStorage.setItem("rememberMe", "true");
        localStorage.setItem("username", ruleForm.username);
        localStorage.setItem("loginTime", Date.now().toString());
      } else {
        localStorage.removeItem("rememberMe");
        localStorage.removeItem("username");
        localStorage.removeItem("loginTime");
      }

      useUserStoreHook()
        .loginByUsername({
          username: ruleForm.username,
          password: ruleForm.password,
          captcha: ruleForm.captcha,
          captchaKey: ruleForm.captchaKey
        })
        .then(res => {
          if (res.success) {
            // 获取后端路由
            return initRouter().then(() => {
              disabled.value = true;
              router
                .push(getTopMenu(true).path)
                .then(() => {
                  message("登录成功", { type: "success" });
                })
                .finally(() => (disabled.value = false));
            });
          } else {
            message("登录失败", { type: "error" });
            // 登录失败后刷新验证码
            getCaptchaImage();
          }
        })
        .finally(() => (loading.value = false));
    }
  });
};

const immediateDebounce: any = debounce(
  formRef => onLogin(formRef),
  1000,
  true
);

useEventListener(document, "keydown", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value);
});

// 检查记住密码和7天免登录
const checkRememberMe = () => {
  const rememberMe = localStorage.getItem("rememberMe");
  const username = localStorage.getItem("username");
  const loginTime = localStorage.getItem("loginTime");

  if (rememberMe === "true" && username) {
    ruleForm.username = username;
    ruleForm.rememberMe = true;

    // 检查是否在7天内
    if (loginTime) {
      const sevenDays = 7 * 24 * 60 * 60 * 1000; // 7天的毫秒数
      const now = Date.now();
      const lastLogin = parseInt(loginTime);

      if (now - lastLogin < sevenDays) {
        // 7天内免登录，直接跳转
        router.push(getTopMenu(true).path);
        return;
      }
    }
  }
};

onMounted(() => {
  getCaptchaImage();
  checkRememberMe();
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="flex-c absolute right-5 top-3">
      <!-- 主题 -->
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h2 class="outline-hidden">{{ title }}</h2>
          </Motion>

          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="loginRules"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入账号',
                    trigger: 'blur'
                  }
                ]"
                prop="username"
              >
                <el-input
                  v-model="ruleForm.username"
                  clearable
                  placeholder="账号"
                  :prefix-icon="useRenderIcon(User)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="password">
                <el-input
                  v-model="ruleForm.password"
                  clearable
                  show-password
                  placeholder="密码"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="200">
              <el-form-item prop="captcha">
                <div class="flex w-full">
                  <el-input
                    v-model="ruleForm.captcha"
                    placeholder="验证码"
                    class="flex-1 mr-2"
                  />
                  <div
                    class="w-24 h-10 border border-gray-300 rounded cursor-pointer flex items-center justify-center"
                    @click="getCaptchaImage"
                  >
                    <img
                      v-if="captchaImg && !captchaLoading"
                      :src="`data:image/png;base64,${captchaImg}`"
                      class="w-full h-full object-contain"
                      alt="验证码"
                    />
                    <span
                      v-else-if="captchaLoading"
                      class="text-xs text-gray-500"
                      >加载中...</span
                    >
                    <span v-else class="text-xs text-gray-500">点击获取</span>
                  </div>
                </div>
              </el-form-item>
            </Motion>

            <Motion :delay="250">
              <div class="flex justify-between items-center mb-4">
                <el-checkbox v-model="ruleForm.rememberMe">
                  记住密码（7天内免登录）
                </el-checkbox>
                <el-button type="primary" link @click="handleForgotPassword">
                  忘记密码？
                </el-button>
              </div>
            </Motion>

            <Motion :delay="300">
              <el-button
                class="w-full mt-4!"
                size="default"
                type="primary"
                :loading="loading"
                :disabled="disabled"
                @click="onLogin(ruleFormRef)"
              >
                登录
              </el-button>
            </Motion>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 忘记密码弹窗 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="忘记密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form label-width="80px">
        <el-form-item label="邮箱地址">
          <el-input
            v-model="forgotPasswordForm.email"
            placeholder="请输入注册时的邮箱地址"
            type="email"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="forgotPasswordVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="forgotPasswordLoading"
            @click="submitForgotPassword"
          >
            发送重置邮件
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
