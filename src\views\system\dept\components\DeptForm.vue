<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { addDept, updateDept, getDeptList } from "@/api/system";

const emit = defineEmits<{
  success: [];
}>();

const dialogVisible = ref(false);
const dialogTitle = ref("新增部门");
const formRef = ref<FormInstance>();
const loading = ref(false);
const isEdit = ref(false);

// 表单数据
const formData = reactive({
  id: "",
  parentId: 0,
  name: "",
  code: "",
  leader: "",
  phone: "",
  email: "",
  sort: 0,
  status: 1,
  remark: ""
});

// 父级部门选项
const parentDeptOptions = ref([]);

// 表单验证规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: "请输入部门名称", trigger: "blur" },
    { min: 2, max: 20, message: "部门名称长度在 2 到 20 个字符", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入部门编码", trigger: "blur" },
    { min: 2, max: 20, message: "部门编码长度在 2 到 20 个字符", trigger: "blur" },
    { pattern: /^[A-Z0-9_]+$/, message: "部门编码只能包含大写字母、数字和下划线", trigger: "blur" }
  ],
  leader: [
    { max: 20, message: "负责人姓名不能超过 20 个字符", trigger: "blur" }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
  ],
  email: [
    { type: "email", message: "请输入正确的邮箱格式", trigger: "blur" }
  ],
  sort: [
    { required: true, message: "请输入排序", trigger: "blur" },
    { type: "number", min: 0, max: 9999, message: "排序必须是 0-9999 的数字", trigger: "blur" }
  ]
});

// 获取父级部门选项
const getParentDeptOptions = async () => {
  try {
    const { data } = await getDeptList();
    const deptTree = buildDeptTree(data);
    parentDeptOptions.value = [
      { id: 0, name: "顶级部门", children: [] },
      ...deptTree
    ];
  } catch (error) {
    console.error("获取父级部门失败:", error);
  }
};

// 构建部门树
const buildDeptTree = (depts: any[], parentId = 0) => {
  const result: any[] = [];
  depts.forEach(dept => {
    if (dept.parentId === parentId) {
      const children = buildDeptTree(depts, dept.id);
      if (children.length > 0) {
        dept.children = children;
      }
      result.push(dept);
    }
  });
  return result;
};

// 打开弹窗
const openDialog = (type = "add", row?: any, parentId?: number) => {
  dialogVisible.value = true;
  isEdit.value = type === "edit";
  dialogTitle.value = type === "edit" ? "编辑部门" : "新增部门";
  
  if (type === "edit" && row) {
    Object.assign(formData, {
      id: row.id,
      parentId: row.parentId,
      name: row.name,
      code: row.code,
      leader: row.leader || "",
      phone: row.phone || "",
      email: row.email || "",
      sort: row.sort || 0,
      status: row.status,
      remark: row.remark || ""
    });
  } else {
    resetForm();
    if (parentId !== undefined) {
      formData.parentId = parentId;
    }
  }
  
  getParentDeptOptions();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: "",
    parentId: 0,
    name: "",
    code: "",
    leader: "",
    phone: "",
    email: "",
    sort: 0,
    status: 1,
    remark: ""
  });
  formRef.value?.clearValidate();
};

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  
  loading.value = true;
  try {
    if (isEdit.value) {
      await updateDept(formData);
      ElMessage.success("编辑成功");
    } else {
      await addDept(formData);
      ElMessage.success("新增成功");
    }
    
    closeDialog();
    emit("success");
  } catch (error) {
    ElMessage.error(isEdit.value ? "编辑失败" : "新增失败");
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="上级部门" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="parentDeptOptions"
          :props="{
            value: 'id',
            label: 'name',
            children: 'children'
          }"
          placeholder="请选择上级部门"
          check-strictly
          :render-after-expand="false"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="部门名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入部门名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部门编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入部门编码"
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人" prop="leader">
            <el-input
              v-model="formData.leader"
              placeholder="请输入负责人"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input
              v-model="formData.phone"
              placeholder="请输入联系电话"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="formData.email"
              placeholder="请输入邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="formData.sort"
              :min="0"
              :max="9999"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :value="1">启用</el-radio>
          <el-radio :value="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
