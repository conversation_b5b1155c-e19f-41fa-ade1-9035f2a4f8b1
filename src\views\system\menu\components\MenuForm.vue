<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { addMenu, updateMenu, getMenuList } from "@/api/system";

const emit = defineEmits<{
  success: [];
}>();

const dialogVisible = ref(false);
const dialogTitle = ref("新增菜单");
const formRef = ref<FormInstance>();
const loading = ref(false);
const isEdit = ref(false);

// 表单数据
const formData = reactive({
  id: "",
  parentId: 0,
  title: "",
  name: "",
  path: "",
  component: "",
  icon: "",
  type: 1,
  sort: 0,
  status: 1,
  permission: "",
  remark: ""
});

// 父级菜单选项
const parentMenuOptions = ref([]);

// 菜单类型选项
const menuTypeOptions = [
  { label: "目录", value: 1 },
  { label: "菜单", value: 2 },
  { label: "按钮", value: 3 }
];

// 表单验证规则
const formRules = reactive<FormRules>({
  title: [
    { required: true, message: "请输入菜单名称", trigger: "blur" },
    { min: 2, max: 20, message: "菜单名称长度在 2 到 20 个字符", trigger: "blur" }
  ],
  name: [
    { required: true, message: "请输入路由名称", trigger: "blur" },
    { pattern: /^[A-Za-z][A-Za-z0-9]*$/, message: "路由名称只能包含字母和数字，且以字母开头", trigger: "blur" }
  ],
  path: [
    { required: true, message: "请输入路由路径", trigger: "blur" }
  ],
  component: [
    { required: true, message: "请输入组件路径", trigger: "blur" }
  ],
  sort: [
    { required: true, message: "请输入排序", trigger: "blur" },
    { type: "number", min: 0, max: 9999, message: "排序必须是 0-9999 的数字", trigger: "blur" }
  ]
});

// 动态验证规则
const dynamicRules = computed(() => {
  const rules = { ...formRules };
  
  // 按钮类型不需要路由名称、路径、组件
  if (formData.type === 3) {
    delete rules.name;
    delete rules.path;
    delete rules.component;
  }
  
  return rules;
});

// 获取父级菜单选项
const getParentMenuOptions = async () => {
  try {
    const { data } = await getMenuList();
    const menuTree = buildMenuTree(data);
    parentMenuOptions.value = [
      { id: 0, title: "顶级菜单", children: [] },
      ...menuTree
    ];
  } catch (error) {
    console.error("获取父级菜单失败:", error);
  }
};

// 构建菜单树
const buildMenuTree = (menus: any[], parentId = 0) => {
  const result: any[] = [];
  menus.forEach(menu => {
    if (menu.parentId === parentId && menu.type !== 3) { // 排除按钮类型
      const children = buildMenuTree(menus, menu.id);
      if (children.length > 0) {
        menu.children = children;
      }
      result.push(menu);
    }
  });
  return result;
};

// 打开弹窗
const openDialog = (type = "add", row?: any, parentId?: number) => {
  dialogVisible.value = true;
  isEdit.value = type === "edit";
  dialogTitle.value = type === "edit" ? "编辑菜单" : "新增菜单";
  
  if (type === "edit" && row) {
    Object.assign(formData, {
      id: row.id,
      parentId: row.parentId,
      title: row.title,
      name: row.name || "",
      path: row.path || "",
      component: row.component || "",
      icon: row.icon || "",
      type: row.type,
      sort: row.sort || 0,
      status: row.status,
      permission: row.permission || "",
      remark: row.remark || ""
    });
  } else {
    resetForm();
    if (parentId !== undefined) {
      formData.parentId = parentId;
    }
  }
  
  getParentMenuOptions();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: "",
    parentId: 0,
    title: "",
    name: "",
    path: "",
    component: "",
    icon: "",
    type: 1,
    sort: 0,
    status: 1,
    permission: "",
    remark: ""
  });
  formRef.value?.clearValidate();
};

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false;
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;
  
  loading.value = true;
  try {
    const submitData = { ...formData };
    
    // 按钮类型清空不需要的字段
    if (submitData.type === 3) {
      submitData.name = "";
      submitData.path = "";
      submitData.component = "";
    }
    
    if (isEdit.value) {
      await updateMenu(submitData);
      ElMessage.success("编辑成功");
    } else {
      await addMenu(submitData);
      ElMessage.success("新增成功");
    }
    
    closeDialog();
    emit("success");
  } catch (error) {
    ElMessage.error(isEdit.value ? "编辑失败" : "新增失败");
  } finally {
    loading.value = false;
  }
};

// 菜单类型变化
const handleTypeChange = () => {
  if (formData.type === 3) {
    // 按钮类型清空相关字段
    formData.name = "";
    formData.path = "";
    formData.component = "";
  }
};

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="700px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="dynamicRules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上级菜单" prop="parentId">
            <el-tree-select
              v-model="formData.parentId"
              :data="parentMenuOptions"
              :props="{
                value: 'id',
                label: 'title',
                children: 'children'
              }"
              placeholder="请选择上级菜单"
              check-strictly
              :render-after-expand="false"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="菜单类型" prop="type">
            <el-radio-group v-model="formData.type" @change="handleTypeChange">
              <el-radio
                v-for="item in menuTypeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="菜单名称" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入菜单名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="菜单图标" prop="icon">
            <el-input
              v-model="formData.icon"
              placeholder="请输入图标名称"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row v-if="formData.type !== 3" :gutter="20">
        <el-col :span="12">
          <el-form-item label="路由名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入路由名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路由路径" prop="path">
            <el-input
              v-model="formData.path"
              placeholder="请输入路由路径"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item v-if="formData.type !== 3" label="组件路径" prop="component">
        <el-input
          v-model="formData.component"
          placeholder="请输入组件路径"
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权限标识" prop="permission">
            <el-input
              v-model="formData.permission"
              placeholder="请输入权限标识"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="formData.sort"
              :min="0"
              :max="9999"
              controls-position="right"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :value="1">启用</el-radio>
          <el-radio :value="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
