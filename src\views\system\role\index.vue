<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getRoleList, addRole, updateRole, deleteRole } from "@/api/system";
import RoleForm from "./components/RoleForm.vue";
import RolePermission from "./components/RolePermission.vue";

import AddFill from "~icons/ri/add-circle-line";
import EditPen from "~icons/ep/edit-pen";
import Delete from "~icons/ep/delete";
import Refresh from "~icons/ep/refresh";
import Search from "~icons/ri/search-line";
import Setting from "~icons/ep/setting";

defineOptions({
  name: "SystemRole"
});

const loading = ref(false);
const tableRef = ref();
const formRef = ref();
const permissionRef = ref();

// 搜索表单
const searchForm = ref({
  name: "",
  code: "",
  status: ""
});

// 表格数据
const tableData = ref([]);
const pagination = ref({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

// 表格列配置
const columns = ref([
  {
    label: "勾选列",
    type: "selection",
    width: 55,
    align: "center",
    headerAlign: "center"
  },
  {
    label: "序号",
    type: "index",
    width: 70,
    align: "center",
    headerAlign: "center"
  },
  {
    label: "角色名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "角色编码",
    prop: "code",
    minWidth: 120
  },
  {
    label: "角色描述",
    prop: "description",
    minWidth: 200
  },
  {
    label: "排序",
    prop: "sort",
    width: 80,
    align: "center"
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80,
    cellRenderer: ({ row }) => (
      <el-tag type={row.status === 1 ? "success" : "danger"}>
        {row.status === 1 ? "启用" : "禁用"}
      </el-tag>
    )
  },
  {
    label: "创建时间",
    prop: "createTime",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
]);

// 选中的行
const selectedRows = ref([]);

// 获取角色列表
const getTableData = async () => {
  loading.value = true;
  try {
    const { data } = await getRoleList({
      ...searchForm.value,
      page: pagination.value.currentPage,
      size: pagination.value.pageSize
    });
    tableData.value = data.list;
    pagination.value.total = data.total;
  } catch (error) {
    console.error("获取角色列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.value.currentPage = 1;
  getTableData();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    name: "",
    code: "",
    status: ""
  };
  pagination.value.currentPage = 1;
  getTableData();
};

// 新增角色
const handleAdd = () => {
  formRef.value?.openDialog();
};

// 编辑角色
const handleEdit = (row: any) => {
  formRef.value?.openDialog("edit", row);
};

// 删除角色
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确认删除角色"${row.name}"吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    await deleteRole({ id: row.id });
    ElMessage.success("删除成功");
    getTableData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

// 权限配置
const handlePermission = (row: any) => {
  permissionRef.value?.openDialog(row);
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请选择要删除的角色");
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedRows.value.length} 个角色吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    const ids = selectedRows.value.map((row: any) => row.id);
    await deleteRole({ ids });
    ElMessage.success("批量删除成功");
    getTableData();
    selectedRows.value = [];
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("批量删除失败");
    }
  }
};

// 表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size;
  getTableData();
};

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page;
  getTableData();
};

// 表单提交成功
const handleFormSuccess = () => {
  getTableData();
};

onMounted(() => {
  getTableData();
});
</script>

<template>
  <div>
    <!-- 搜索表单 -->
    <el-form
      :model="searchForm"
      :inline="true"
      class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="角色名称：" prop="name">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入角色名称"
          clearable
          class="w-[180px]"
        />
      </el-form-item>
      <el-form-item label="角色编码：" prop="code">
        <el-input
          v-model="searchForm.code"
          placeholder="请输入角色编码"
          clearable
          class="w-[180px]"
        />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="w-[180px]"
        >
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="handleReset">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="角色管理" :columns="columns" @refresh="getTableData">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon(AddFill)"
          @click="handleAdd"
        >
          新增角色
        </el-button>
        <el-button
          type="danger"
          :icon="useRenderIcon(Delete)"
          :disabled="selectedRows.length === 0"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </template>
      
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @selection-change="handleSelectionChange"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(EditPen)"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon(Setting)"
              @click="handlePermission(row)"
            >
              权限配置
            </el-button>
            <el-button
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon(Delete)"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>

  <!-- 角色表单弹窗 -->
  <RoleForm ref="formRef" @success="handleFormSuccess" />
  
  <!-- 权限配置弹窗 -->
  <RolePermission ref="permissionRef" />
</template>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
