<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getOnlineUserList, forceLogout } from "@/api/monitor";

import Refresh from "~icons/ep/refresh";
import Search from "~icons/ri/search-line";
import Delete from "~icons/ep/delete";

defineOptions({
  name: "MonitorOnline"
});

const loading = ref(false);
const tableRef = ref();

// 搜索表单
const searchForm = ref({
  username: "",
  ipaddr: ""
});

// 表格数据
const tableData = ref([]);
const total = ref(0);
const pagination = ref({
  page: 1,
  size: 10
});

// 表格列配置
const columns = ref([
  {
    label: "会话ID",
    prop: "sessionId",
    minWidth: 200
  },
  {
    label: "用户名",
    prop: "username",
    minWidth: 120
  },
  {
    label: "昵称",
    prop: "nickname",
    minWidth: 120
  },
  {
    label: "部门",
    prop: "deptName",
    minWidth: 120
  },
  {
    label: "IP地址",
    prop: "ipaddr",
    minWidth: 130
  },
  {
    label: "登录地点",
    prop: "loginLocation",
    minWidth: 150
  },
  {
    label: "浏览器",
    prop: "browser",
    minWidth: 120
  },
  {
    label: "操作系统",
    prop: "os",
    minWidth: 120
  },
  {
    label: "登录时间",
    prop: "loginTime",
    minWidth: 160
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
]);

// 获取在线用户列表
const getTableData = async () => {
  loading.value = true;
  try {
    const { data } = await getOnlineUserList({
      ...searchForm.value,
      page: pagination.value.page,
      size: pagination.value.size
    });
    tableData.value = data.list;
    total.value = data.total;
  } catch (error) {
    console.error("获取在线用户列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.value.page = 1;
  getTableData();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    username: "",
    ipaddr: ""
  };
  pagination.value.page = 1;
  getTableData();
};

// 强制下线
const handleForceLogout = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确认强制下线用户"${row.username}"吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    await forceLogout({ sessionId: row.sessionId });
    ElMessage.success("强制下线成功");
    getTableData();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("强制下线失败");
    }
  }
};

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.value.size = size;
  getTableData();
};

const handleCurrentChange = (page: number) => {
  pagination.value.page = page;
  getTableData();
};

onMounted(() => {
  getTableData();
});
</script>

<template>
  <div>
    <!-- 搜索表单 -->
    <el-form
      :model="searchForm"
      :inline="true"
      class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="用户名：" prop="username">
        <el-input
          v-model="searchForm.username"
          placeholder="请输入用户名"
          clearable
          class="w-[180px]"
        />
      </el-form-item>
      <el-form-item label="IP地址：" prop="ipaddr">
        <el-input
          v-model="searchForm.ipaddr"
          placeholder="请输入IP地址"
          clearable
          class="w-[180px]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon(Search)"
          :loading="loading"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon(Refresh)" @click="handleReset">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="在线用户" :columns="columns" @refresh="getTableData">
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          ref="tableRef"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <el-button
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon(Delete)"
              @click="handleForceLogout(row)"
            >
              强制下线
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
