<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getDeptList } from "@/api/system";

const emit = defineEmits<{
  deptSelect: [deptId: string];
}>();

const loading = ref(false);
const treeRef = ref();
const treeData = ref([]);
const defaultProps = {
  children: "children",
  label: "name"
};

// 获取部门树数据
const getTreeData = async () => {
  loading.value = true;
  try {
    const { data } = await getDeptList();
    treeData.value = [
      {
        id: "",
        name: "全部部门",
        children: buildTree(data)
      }
    ];
  } catch (error) {
    console.error("获取部门树失败:", error);
  } finally {
    loading.value = false;
  }
};

// 构建树形结构
const buildTree = (depts: any[], parentId = 0) => {
  const result: any[] = [];
  depts.forEach(dept => {
    if (dept.parentId === parentId) {
      const children = buildTree(depts, dept.id);
      if (children.length > 0) {
        dept.children = children;
      }
      result.push(dept);
    }
  });
  return result;
};

// 节点点击事件
const handleNodeClick = (data: any) => {
  emit("deptSelect", data.id);
};

// 刷新树
const refresh = () => {
  getTreeData();
};

onMounted(() => {
  getTreeData();
});

// 暴露方法给父组件
defineExpose({
  refresh
});
</script>

<template>
  <el-card class="h-full">
    <template #header>
      <div class="flex justify-between items-center">
        <span class="font-medium">部门列表</span>
        <el-button
          type="primary"
          link
          @click="refresh"
        >
          刷新
        </el-button>
      </div>
    </template>
    
    <div v-loading="loading" class="h-full">
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="defaultProps"
        :expand-on-click-node="false"
        :default-expand-all="true"
        highlight-current
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="flex items-center">
            <el-icon class="mr-1">
              <component :is="data.id === '' ? 'OfficeBuilding' : 'User'" />
            </el-icon>
            <span>{{ node.label }}</span>
          </div>
        </template>
      </el-tree>
    </div>
  </el-card>
</template>

<style lang="scss" scoped>
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: var(--el-tree-node-hover-bg-color);
}

:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}
</style>
